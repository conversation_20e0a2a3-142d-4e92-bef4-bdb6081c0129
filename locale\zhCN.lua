
local __addon, __private = ...;
__private.L = __private.L or { EMOTE = {}, };
local L = __private.L;

L.EMOTE["zhCN"] = {
    Angel = "天使",
    Angry = "生气",
    Biglaugh = "大笑",
    Clap = "鼓掌",
    Cool = "酷",
    Cry = "哭",
    Cute = "可爱",
    Despise = "鄙视",
    Dreamsmile = "美梦",
    Embarras = "尴尬",
    Evil = "邪恶",
    Excited = "兴奋",
    Faint = "晕",
    Fight = "打架",
    Flu = "流感",
    Freeze = "呆",
    Frown = "皱眉",
    Greet = "致敬",
    Grimace = "鬼脸",
    Growl = "龇牙",
    Happy = "开心",
    Heart = "心",
    Horror = "恐惧",
    Ill = "生病",
    Innocent = "无辜",
    Kongfu = "功夫",
    Love = "花痴",
    Mail = "邮件",
    Makeup = "化妆",
    <PERSON> = "马里奥",
    Meditate = "沉思",
    Miserable = "可怜",
    Okay = "好",
    Pretty = "漂亮",
    Puke = "吐",
    Shake = "握手",
    Shout = "喊",
    Silent = "闭嘴",
    Shy = "害羞",
    Sleep = "睡觉",
    Smile = "微笑",
    Suprise = "吃惊",
    Surrender = "失败",
    Sweat = "流汗",
    Tear = "流泪",
    Tears = "悲剧",
    Think = "想",
    Titter = "偷笑",
    Ugly = "猥琐",
    Victory = "胜利",
    Volunteer = "雷锋",
    Wronged = "委屈",
};

if GetLocale() ~= "zhCN" then
	return;
end
L.Locale = "zhCN";
L.ExactLocale = true;

L.TABSHORT = {
	SAY = "说",
	PARTY = "队",
	RAID = "团",
	RAID_WARNING = "通",
	INSTANCE_CHAT = "副",
	GUILD = "会",
	YELL = "喊",
	WHISPER = "密",
	OFFICER = "官",
	GENERAL = "综",
	TRADE = "交",
	LOCAL_DEFENSE = "本",
	LOOK_FOR_GROUP = "组",
	BF_WORLD = "世",
};
L.LocalizedChannelName = {
	GENERAL = GENERAL,
	TRADE = TRADE,
	LOCAL_DEFENSE = "本地防务",
	LOOK_FOR_GROUP = LOOK_FOR_GROUP,
	BF_WORLD = "大脚世界频道",
};
L.SHORTNAME_NORMALGLOBALFORMAT = {
	CHAT_WHISPER_GET = "[密]%s说: ",
	CHAT_WHISPER_INFORM_GET = "[密]对%s说: ",
	CHAT_MONSTER_WHISPER_GET = "[密]%s说: ",
	CHAT_BN_WHISPER_GET = "[密]%s说: ",
	CHAT_BN_WHISPER_INFORM_GET = "[密]对%s说: ",
	CHAT_BN_CONVERSATION_GET = "%s:",
	CHAT_BN_CONVERSATION_GET_LINK = "|Hchannel:BN_CONVERSATION:%d|h[%s.对话]|h",
	CHAT_SAY_GET = "[说]%s: ",
	CHAT_MONSTER_SAY_GET = "[说]%s: ",
	CHAT_YELL_GET = "[喊]%s: ",
	CHAT_MONSTER_YELL_GET = "[喊]%s: ",
	CHAT_GUILD_GET = "|Hchannel:GUILD|h[会]|h%s: ",
	CHAT_OFFICER_GET = "|Hchannel:OFFICER|h[官]|h%s: ",
	CHAT_PARTY_GET = "|Hchannel:PARTY|h[队]|h%s: ",
	CHAT_PARTY_LEADER_GET = "|Hchannel:PARTY|h[队]|h%s: ",
	CHAT_MONSTER_PARTY_GET = "|Hchannel:PARTY|h[队]|h%s: ",
	CHAT_PARTY_GUIDE_GET = "|Hchannel:PARTY|h[副]|h%s: ",
	CHAT_INSTANCE_CHAT_GET = "|Hchannel:BG|h[副]|h%s: ",
	CHAT_INSTANCE_CHAT_LEADER_GET = "|Hchannel:BG|h[副]|h%s: ",
	CHAT_RAID_GET = "|Hchannel:RAID|h[团]|h%s: ",
	CHAT_RAID_LEADER_GET = "|Hchannel:RAID|h[团]|h%s: ",
	CHAT_RAID_WARNING_GET = "[团]%s: ",

	CHAT_AFK_GET = "[AFK]%s: ",
	CHAT_DND_GET = "[DND]%s: ",
	CHAT_EMOTE_GET = "%s: ",
	CHAT_PET_BATTLE_INFO_GET = "|Hchannel:PET_BATTLE_INFO|h[宠]|h: ",
	CHAT_PET_BATTLE_COMBAT_LOG_GET = "|Hchannel:PET_BATTLE_COMBAT_LOG|h[宠]|h: ",
	CHAT_CHANNEL_LIST_GET = "|Hchannel:频道:%d|h[%s]|h",
	CHAT_CHANNEL_GET = "%s: ",
};
L.SHORTNAME_CHANNELHASH = {
	["综合"] = "综",
	["交易"] = "交",
	["本地防务"] = "本",
	["寻求组队"] = "组",
	["大脚世界频道"] = "世",
	["大脚世界频道1"] = "世",
	["大脚世界频道2"] = "世",
	["大脚世界频道3"] = "世",
	["大脚世界频道4"] = "世",
	["大脚世界频道5"] = "世",
	["大脚世界频道6"] = "世",
	["大脚世界频道7"] = "世",
	["大脚世界频道8"] = "世",
	["大脚世界频道9"] = "世",
	["大脚世界频道10"] = "世",
};


L.SETTINGCATEGORY = {
	GENERAL = "外观设置",
	MISC = "聊天细节",
	CHANNELTAB = "频道切换",
	CHATFILTER = "聊天过滤",
	HIGHLIGHT = "聊天高亮",
	COPY = "聊天复制",
	COMPANION = "公会好友",
	UTILS = "额外工具",
};
L.SETTING = {
	general = {
		detailedtip = "按钮提示中显示详细提示【关闭前请确定了解所有按钮操作】",
	},
	docker = {
		Position = "聊天切换条位置",
		AutoAdjustEditBox = "自动调整输入框位置",
		Direction = "聊天条方向",
		alpha = "显示透明度",
		FadInTime = "渐显时间/秒",
		FadedAlpha = "渐隐透明度",
		FadeOutTime = "渐隐时间/秒",
		FadeOutDelay = "渐隐延迟/秒",
		Backdrop = "按钮栏背景",
		BackdropColor = "背景颜色",
		BackdropAlpha = "背景透明度",
		PinSize = "按钮大小",
		PinInt = "按钮间距",
		PinStyle = "按钮风格(自动同步更改其它标签中的风格设置)",
		--
		["below.editbox"] = "输入框下方",
		["above.editbox"] = "输入框上方",
		["manual"] = "自由拖动 `快捷键: CTRL",
		["RIGHT"] = "由左到右",
		["LEFT"] = "由右到左",
		["DOWN"] = "由上到下",
		["UP"] = "由下到上",
		["manual"] = "自由设置",
		["char"] = "文字",
		["char.blz"] = "暴雪",
		["circle.blur"] = "毛边圆",
		["circle"] = "圆圈",
		["square.blur"] = "毛边方块",
		["square"] = "方块",
	},
	channeltab = {
		toggle = "开启频道切换条",
		--
		SAY = "|cffffffff" .. SAY .. "|r",
		PARTY = "|cffaaaaff" .. PARTY .. "|r",
		RAID = "|cffff7f00" .. RAID .. "|r",
		RAID_WARNING = "|cffff4800" .. RAID_WARNING .. "|r",
		INSTANCE_CHAT = "|cffff7f00" .. INSTANCE_CHAT .. "|r",
		GUILD = "|cff40ff40" .. GUILD .. "|r",
		YELL = "|cffff4040" .. YELL .. "|r",
		WHISPER = "|cffff80ff" .. WHISPER .. "|r",
		OFFICER = "|cff40c040" .. OFFICER .. "|r",
		GENERAL = "|cffffc0c0" .. GENERAL .. "|r",
		TRADE = "|cffffc0c0" .. TRADE .. "|r",
		LOCAL_DEFENSE = "|cffffc0c0" .. L.LocalizedChannelName.LOCAL_DEFENSE .. "|r",
		LOOK_FOR_GROUP = "|cffffc0c0" .. L.LocalizedChannelName.LOOK_FOR_GROUP .. "|r",
		BF_WORLD = "|cffffc0c0世界频道|r",
		UNMANAGEDCHANNEL = "|cffffc0c0其它频道|r",
		PinStyle = "按钮风格",
		UseColor = "按频道着色按钮",
		LeaveChannelModifier = "离开频道按键 (按住此按键，点击频道按钮将离开频道)",
		AutoJoinWorld = "自动加入世界频道",
		AutoAddChannelToDefaultChatFrame = "加入任何一个频道时，自动将其加入到第一个聊天窗口中",
		ChannelBlockButton_BLZ = "公共频道开关",
		ChannelBlockButton_World = "世界频道开关",
		ChannelBlockButton_Size = "开关按钮大小",
		--
		["none"] = "关闭此功能",
		["char"] = "文字",
		["char.blz"] = "暴雪",
		["circle.blur"] = "毛边圆",
		["circle"] = "圆圈",
		["square.blur"] = "毛边方块",
		["square"] = "方块",
	},
	chatfilter = {
		toggle = "开启聊天过滤",
		--
		CaseInsensitive = "忽略大小写",
		StrSet = "设置关键词",
		StrSetTip = "每行一个关键词",
		NameSet = "设置黑名单",
		NameSetTip = "每行一个名字\n|cff00ff00#|r开头表示名字包含即屏蔽",
		Rep = "开启重复信息过滤",
		RepInterval = "重复信息过滤间隔",
		RepeatedSentence = "合并聊天中的重复语句",
		ButtonInDock = "在聊天条里显示按钮",
		PinStyle = "按钮风格",
		--
		["char"] = "图片",
		["char.blz"] = "暴雪",
	},
	emote = {
		toggle = "开启聊天表情",
		--
		IconInEditBox = "在聊天输入框中显示图标",
		PinStyle = "聊天切换条按钮风格",
		--
		["char"] = "图片",
		["char.blz"] = "暴雪",
		["circle.blur"] ="暴雪",
		["circle"] = "暴雪",
		["square.blur"] = "暴雪",
		["square"] = "暴雪",
	},
	highlight = {
		toggle = "开启关键词高亮",
		--
		CaseInsensitive = "忽略大小写",
		StrSet = "设置关键词",
		StrSetTip = "每行一个关键词\n可以在关键词后面以#接上六位颜色代码，例如：副本#FF0000，即将‘副本’高亮显示为‘|cffff0000副本|r’",
		color = "设置颜色",
		format = "设置格式",
		ShowMatchedOnly = "只显示包含关键词的聊天 `每次登陆或重载插件都会自动关闭",
		["ShowMatchedOnly.CHANNEL"] = "过滤 |cffffc0c0[频道]|r",
		["ShowMatchedOnly.SAY-YELL"] = "过滤 |cffffffff[说]|r、|cffff4040[喊]|r",
		["ShowMatchedOnly.NORMAL"] = "过滤 所有其他信息类型(|cffff80ff[悄悄话]|r、|cff40ff40[公会]|r、|cffaaaaff[队伍]|r、|cffff7f00[团队]|r等)",
		KeepShowMatchedOnly = "禁止自动关闭【|cffff0000！！！记性差不要用这个！！！|r】",
		ButtonInDock = "在聊天条里显示按钮",
		PinStyle = "按钮风格",
		--
		["#HL#"] = "NAXX",
		[">>#HL#<<"] = ">>NAXX<<",
		["**#HL#**"] = "**NAXX**",
		["[[#HL#]]"] = "[[NAXX]]",
		["char"] = "文字",
		["char.blz"] = "暴雪",
	},
	copy = {
		toggle = "开启聊天复制功能，点击时间戳复制",
		--
		color = "设置颜色",
		format = "设置格式",
		--
		["none"] = "*",
		["%H:%M"] = "15:27",
		["%H:%M:%S"] = "15:27:32",
		["%I:%M"] = "03:27",
		["%I:%M:%S"] = "03:27:32",
		["%I:%M %p"] = "03:27 PM",
		["%I:%M:%S %p"] = "03:27:32 PM",
	},
	shortchattype = {
		toggle = "开启短频道名",
		--
		format = "设置短频道名格式",
		--
		["n.w"] = "数字.短名 (1.综)",
		["n"] = "数字 (1)",
		["w"] = "短名 (综)",
	},
	misc = {
		Font = "字体",
		FontFlag = "阴影",
		ChatFrameToBorder = "允许将聊天框拖动到紧靠边缘",
		ColoredPlayerName = "按职业着色聊天框中玩家名字",
		HoverHyperlink = "显示聊天框中鼠标指向物品提示",
		ChatHyperlink = "在不支持超链接的频道中使用|cff1eff00[物品]|r/|cff71d5ff[法术]|r链接",
		TabChangeChatType = "TAB键切换频道",
		StickyWhisper = "聊天框保持上次的|cffff80ff私聊|r状态",
		StickyBNWhisper = "聊天框保持上次的|cff00fff6战网聊天|r状态",
		StickyChannel = "聊天框保持上次的|cffffc0c0公共频道聊天|r状态",
		ArrowKey = "聊天框激活时使用方向键移动光标 (而不是控制角色移动)",
		ArrowHistory = "使用上下箭头自动输入历史消息 (不需要ALT键)",
		--
		["none"] = "无阴影",
		["OUTLINE"] = "薄阴影",
		["THICKOUTLINE"] = "厚阴影",
	},
	utils = {
		StatReport = "属性报告",
		DBMPull = "倒计时按钮",
		DBMPullLen = "倒计时长度",
		roll = "ROLL点按钮",
		ReadyCheck = "就位确认按钮",
	},
	companion = {
		SavedInDB = "将缓存的玩家信息存储到设置文件中【将增加内存占用和配置文件体积】",
		ShowLevel = "显示玩家等级",
		ShowSubGroup = "显示团队成员小队编号",
		PlayerLinkFormat = "玩家链接格式",
		WelToGuild = "开启公会成员欢迎",
		WelToGuildStrSet = "设置欢迎语",
		WelToGuildStrSetTip = "每行一句欢迎语，每位新成员随机选择一句。\n|cff7fff7f#PLAYER#|r = 自己的名字 |cff7fff7f#PCLASS#|r = 自己的职业 |cff7fff7f#PRACE#|r = 自己的种族 |cff7fff7f#NAME#|r = 新成员名字 |cff7fff7f#CLASS#|r = 新成员职业 |cff7fff7f#LEVEL#|r = 新成员等级 |cff7fff7f#AREA#|r = 新成员位置",
		WelToGuildDelay = "延迟发送欢迎语",
		NewMemberNotice = "新成员通知",
		NewMemberNoticeStr = "设置通知格式",
		NewMemberNoticeStrTip = "首个不为空的行有效。\n|cff7fff7f#NAME#|r代表新成员名字 |cff7fff7f#CLASS#|r代表新成员职业 |cff7fff7f#LEVEL#|r代表新成员等级 |cff7fff7f#AREA#|r代表新成员位置",
		--
		["#INDEX.##NAME##LEVEL#"] = "1.Alex|cffffff0070|r",
		["#INDEX.##NAME##:LEVEL#"] = "1.Alex:|cffffff0070|r",
		["#INDEX.##NAME##(LEVEL)#"] = "1.Alex(|cffffff0070|r)",
		["#(INDEX)##NAME##LEVEL#"] = "(1)Alex|cffffff0070|r",
		["#(INDEX)##NAME##:LEVEL#"] = "(1)Alex:|cffffff0070|r",
		["#LEVEL:##NAME## INDEX#"] = "|cffffff0070|r:Alex 1",
		--
		["def.WelToGuildStrSet"] = "欢迎 #NAME# ！",
		["def.NewMemberNoticeStr"] = "** 新公会成员：Lv#LEVEL# #CLASS# #NAME# **",
	},
};

L.STATREPORT = {
	melee = "近战",
	spell = "法术",
	ranged = "远程",
	tank = "坦克",
	heal = "治疗",
	SHAPESHIFTFORMFIRST = "先变熊或变豹",
	["163UI"] = "有爱属性通报: ",
	ItemLevel = "装等",
};

L.TIP = {
	-- channeltab = "",
	chatfilter = "聊天过滤",
	emote = "表情",
	highlight = "关键词高亮",
	StatReport = "属性报告",
	DBMPull = "倒计时按钮",
	roll = "ROLL",
	ReadyCheck = "就位确认",
};
L.DETAILEDTIP = {
	DockerDrag = { "按住CTRL拖动来移动位置", },
	channeltab = { "左键：切换频道", "右键：屏蔽该频道", },
	channeltabjoin = { "点击加入频道" };
	chatfilter = { "左键：启用/关闭", "右键：设置关键词", "ALT右键：设置黑名单", },
	-- emote = "",
	highlight = { "左键：启用/关闭", "右键：设置关键词", },
	StatReport = { "左键：自动匹配属性报告", "右键：手选报告类型", },
	DBMPull = { "左键：开始倒计时按钮", "右键：取消倒计时", },
	-- roll = "ROLL",
	-- ReadyCheck = "就位确认",
};

L["Find profiles of alaChat_Classic. Do you want to upgrade it and overwrite current setting?"] = "发现alaChat_Classic旧版本的设置，是否使用它覆盖当前设置？";
